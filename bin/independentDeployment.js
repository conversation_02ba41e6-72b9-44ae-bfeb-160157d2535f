const fs = require('fs-extra')
const path = require('path')
const url = require('url')
const modules = process.argv.slice(2)
// 被读取的文件夹地址
const filePath = path.resolve(__dirname, '../src/modules')
// 所有模块名
let fileAllModules = []
// 收集所有的文件路径
const fileDisplay = filePath => {
  fileAllModules = fs.readdirSync(filePath)
  let flag = true
  modules.forEach(module => {
    if (!fileAllModules.includes(module)) {
      flag = false
      console.log(new Error(`${module}模块不存在，请重试！`))
    }
  })
  return flag
}

/**
 * 复制文件夹到目标文件夹
 * @param {string} src 源目录
 * @param {string} dest 目标目录
 * @param {function} callback 回调
 */
const copyDir = (src, dest, callback) => {
  const copy = (copySrc, copyDest) => {
    fs.readdir(copySrc, (err, list) => {
      if (err) {
        callback(err)
        return
      }
      list.forEach(item => {
        // 在这里进行劫持
        // /Users/<USER>/Desktop/yunsopWord/yunsop/xy/src/modules/systemPublicSeting/views/cueSetting/allocation
        let resultArr = copySrc.split('/')
        let index = resultArr.findIndex(str => str == 'modules')

        if (
          (resultArr.includes('modules') && modules.includes(item)) ||
          modules.includes(resultArr[index + 1]) ||
          index < 0
        ) {
          const ss = path.resolve(copySrc, item)
          fs.stat(ss, (err, stat) => {
            if (err) {
              callback(err)
            } else {
              const curSrc = path.resolve(copySrc, item)
              const curDest = path.resolve(copyDest, item)

              if (stat.isFile()) {
                // 文件，直接复制
                fs.createReadStream(curSrc).pipe(fs.createWriteStream(curDest))
              } else if (stat.isDirectory()) {
                // 目录，进行递归
                fs.mkdirSync(curDest, { recursive: true })
                copy(curSrc, curDest)
              }
            }
          })
        }
      })
    })
  }

  fs.access(dest, err => {
    if (err) {
      // 若目标目录不存在，则创建
      fs.mkdirSync(dest, { recursive: true })
    }
    copy(src, dest)
  })
}
function copyFoldRecursiveSync(target, callback) {
  if (!fileDisplay(filePath)) {
    return
  }
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target)
  }
  const files = fs.readdirSync(path.resolve(__dirname, '../'))
  const gitignore = fs
    .readFileSync(path.resolve(__dirname, '../.gitignore'), 'utf-8')
    .split('\n')
    .concat([
      'node_modules',
      '.git',
      'yarn.lock',
      '.VSCodeCounter',
      'bin',
      'stats.html'
    ])

  files.forEach(fileName => {
    if (!gitignore.includes(fileName)) {
      let sourcePath = path.resolve(__dirname, `../${fileName}`)
      let targetPath = path.resolve(__dirname, `./${target}`)
      const stat = fs.statSync(sourcePath)
      if (stat.isFile()) {
        fs.copyFileSync(sourcePath, `${target}/${fileName}`)
      } else {
        copyDir(sourcePath, `${target}/${fileName}`)
      }
    }
  })
  callback && callback()
}
let getRandomName = (function () {
  let name
  return function () {
    if (name) {
      return name
    }
    name = 'xy' + (Math.random() * 10000).toFixed(0)
    return name
  }
})()
copyFoldRecursiveSync(path.resolve(__dirname, getRandomName()))
