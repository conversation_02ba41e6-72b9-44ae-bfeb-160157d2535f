<template>
  <!-- 选择机构 -->
  <select-branch :merchants="state.merchants" v-model:show="state.show" @success="success" />
  <div class="login">

    <div class="logo-left-box">
      <div class="logo">
        <img src="../../../assets/new-icon/logo.png" alt="" />
      </div>
      <!-- 登录页 -->
      <div class="left-box">
        <div class="login-box animate__animated animate__backInDown">
          <div class="welcome-text">
            欢迎使用题库中台
            <div class="line"></div>
          </div>
          <!-- 账号密码 短信 登录 -->
          <!--          <div class="tabs" @click="changeLoginType">-->
          <!--            <div-->
          <!--              class="bg"-->
          <!--              :class="{ sao: loginType == '1' || loginType == '2' }"-->
          <!--            ></div>-->
          <!--            <div class="tabs-hover">-->
          <!--              {{-->
          <!--                loginType == '3' || loginType == '4' ? '账号登录' : '扫码登录'-->
          <!--              }}-->
          <!--            </div>-->
          <!--          </div>-->
          <!-- 扫一扫登录 -->
          <transition name="sao" mode="out-in">
            <div>
              <div class="sao-sao" v-show="loginType == '3' || loginType == '4'">
                <dingtalk @loginBtn="loginBtn" v-show="loginType == '3'" :loginType="loginType" />
                <wchat @loginBtn="loginBtn" v-show="loginType == '4'" :loginType="loginType" />
                <!-- 切换企业微信登陆 -->
                <div class="chartBox">
                  <div class="checkoutLoginType">
                    <!-- 钉钉 -->
                    <img
                      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/public/167384057972589a5167384057972538797_ding.png"
                      alt="" v-show="loginType == '4'" @click="loginType = '3'" />
                    <!-- 企业微信 -->
                    <img
                      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/public/16738406610673783167384066106799344_qw.png"
                      alt="" v-show="loginType == '3'" @click="loginType = '4'" />
                  </div>
                  <div class="checkoutLoginType">
                    <img
                      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/public/16751582976992a3e167515829769944608_tu.png"
                      alt="" class="xy" />
                    <img class="imgTwo"
                      src="https://yakaixin.oss-cn-beijing.aliyuncs.com/public/167515830755123be167515830755184167_tidai.png"
                      alt="" />
                  </div>
                </div>
                <div class="line-checkout-bottom"></div>
                <div class="question">
                  <div class="text">
                    <!-- <span>登录遇到问题？</span> -->
                    <img src="../../../assets/imgs/Login/text2.png" alt="" />
                  </div>
                  <div class="text">
                    <img src="../../../assets/imgs/Login/lxcp.png" alt="" class="active" />
                    <img src="../../../assets/imgs/Login/text3.png" alt="" class="current" />
                    <div class="zhidong">
                      <!-- 联系志东 -->
                      <div class="zhidong-text">请使用钉钉扫码</div>
                      <div class="img">
                        <img
                          src="https://yakaixin.oss-cn-beijing.aliyuncs.com/public/16510603839371de4165106038393760397_zhidong.61a364f1.jpg"
                          alt="" />
                      </div>
                      <div class="zhidong-text">信息中心-粱志东</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="admin-password" v-show="loginType == '1' || loginType == '2'">
                <!-- 区分账号密码登录 -->
                <!-- 账号密码登录 -->
                <div class="logintype form" v-if="loginType == '1'">
                  <div class="form-item phone">
                    <input type="text" placeholder="请输入账号" v-model="form.account" @keyup.enter="next" style="background: #f9fafc;border: 1px solid #ededed;" />
                  </div>
                  <div class="form-item password">
                    <input :type="state.eya ? 'password' : 'text'" placeholder="请输入密码" ref="password"
                      v-model="form.password" @keyup.enter="loginBtn"  style="background: #f9fafc;border: 1px solid #ededed;"/>
                    <div class="eya" :class="{ 'eya-active': !state.eya }" @click="state.eya = !state.eya"></div>
                  </div>
                  <div class="login-btn">
                    <div class="button" @click="loginBtn">登录</div>
                  </div>
                  <div class="form-item">
                    <div class="flex">
                      <div class="forget-password">
                        <div @click="state.forget = !state.forget" class="select" :class="{ selected: state.forget }">
                        </div>
                        <div class="text">
                          <span @click="state.forget = !state.forget">同意</span>
                          <span class="btn" @click="goPrivacy">《医学云隐私政策》</span>
                        </div>
                      </div>
                      <!-- 隐藏：忘记密码功能
                      <div class="forget">
                        <span class="text" @click="forget">忘记密码?</span>
                      </div> -->
                    </div>
                  </div>
                </div>
                <!-- 短信登录 -->
                <div class="logintype short-message form" v-if="loginType == '2'">
                  <div class="form-item phone">
                    <input type="number" placeholder="请输入手机号码" v-model="form.phone" />
                  </div>
                  <div class="form-item password">
                    <input placeholder="请输入验证码" v-model="form.code" @keyup.enter="loginBtn" />
                    <div class="getCode" @click="sendCode">
                      {{ !sending ? '获取验证码' : `${time}s后可重新发送` }}
                    </div>
                  </div>
                  <div class="login-btn">
                    <div class="button" @click="loginBtn">登录</div>
                  </div>
                  <div class="form-item">
                    <div class="flex">
                      <div class="forget-password">
                        <div @click="state.forget = !state.forget" class="select" :class="{ selected: state.forget }">
                        </div>
                        <div class="text">
                          <span @click="state.forget = !state.forget">同意</span>
                          <span class="btn" @click="forget">《医学云隐私政策》</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 账号密码登录 -->
                <!-- 隐藏：更多方式的验证码登录
                <div class="tabs-admin">
                  <div class="more">更多方式</div>
                  <el-button @click="loginType = '2'" v-if="loginType != '2'">
                    <div class="tabs-admin-btn">
                      <img
                        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17273434823771b5c172734348237740102_yanzhengma.png"
                        alt=""
                      />
                      <span> 验证码登录</span>
                    </div>
                  </el-button>
                  <el-button @click="loginType = '1'" v-if="loginType != '1'">
                    <div class="tabs-admin-btn">
                      <img
                        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/172734861847322a2172734861847383901_suo.png"
                        alt=""
                      />
                      <span> 账号登录</span>
                    </div>
                  </el-button>
                </div> -->
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
    <div class="bg-animate-box">
      <img src="@/assets/new-icon/big-background.png" alt="" style="width: 100%;height: 100vh;" />

      <!-- <el-carousel :height="'100vh'" arrow="never">
        <el-carousel-item>
          <div class="bg-animate">
            <img
              src="../../../assets/imgs/Login/login-bga.png"
              alt=""
            />
          </div>
        </el-carousel-item>
        <el-carousel-item>
          <div class="bg-animate">
            <img
              src="../../../assets/imgs/Login/login-bgb.png"
              alt=""
            />
          </div>
        </el-carousel-item>
      </el-carousel> -->
    </div>
  </div>
  <div class="footer">
    <div class="options">
      <a @click="operation.down">相关下载</a>
      <a @click="operation.instructions">使用说明</a>
      <a @click="operation.contactManagement">联系管理</a>
    </div>
    <div class="company">
      Copyright @ 2020-2030 北京医学云科技有限公司
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, reactive, ref, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { cusElMessage, ElMessage } from '@/utils/element'
import { useStore } from 'vuex'
import { setStore, getStore, clearStore } from '@/utils/localStore'
import { expPhone } from '@/utils/Reg'
import selectBranch from '@/components/BusinessCenter/public/select-branch.vue'
import type { branchinfo } from '@/store/modules/User/index.type'
import { getMerchant, sendcode } from '@/api'
import { uncultivated } from '@/config'
import dingtalk from '@/components/AppSystem/login/dingtalk.vue'
import wchat from '@/components/AppSystem/login/wchat.vue'
import { useAutoLogin } from '@/CompositionHooksApi/useAutoLogin'
import { isScss } from '@/utils'
export default defineComponent({
  name: 'Login',
  props: {
    isBgAnimate: {
      // 是否显示左侧动态效果图
      type: Boolean,
      default: true
    },
    background: {
      // 登录页背景图
      type: String,
      default: `url('https://yakaixin.oss-cn-beijing.aliyuncs.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png') no-repeat bottom left`
      // default:
      //   'https://yakaixin.oss-cn-beijing.aliyuncs.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png'
    }
  },
  components: { selectBranch, dingtalk, wchat },
  setup() {
    const router = useRouter()
    const form = reactive({
      // 账号密码
      account: '',
      password: '',
      // 手机密码
      phone: '',
      code: ''
    })
    const state = reactive({
      forget: false,
      merchants: [],
      show: false,
      eya: true
    })
    const data = reactive({
      loginType: '1', // 1 账号密码 2 短信登录 3 扫码登录(钉钉) 4. 扫码登陆（企业微信）
      // 验证码是否已经送
      sending: false,
      time: 60 // 60s倒计时
    })
    const store = useStore()
    function passwordInit() {
      try {
        let account = getStore('__account__')
        console.log(account)
        let password = getStore('__password__')
        if (account && password) {
          form.account = account
          form.password = password
          state.forget = true
        } else {
          state.forget = false
        }
      } catch (e) {
        console.log(e)
      }
    }
    passwordInit()
    function forget() {
      if (state.forget) {
        // 记住密码
        setStore('__account__', form.account)
        setStore('__password__', form.password)
      } else {
        // 清除登录
        clearStore('__account__')
        clearStore('__password__')
      }
    }
    function isOneData(merchants: any[]): boolean {
      if (
        merchants &&
        merchants?.length == 1 &&
        merchants[0]?.brand?.length &&
        merchants[0].brand.length == 1
      ) {
        success({
          merchant: merchants[0],
          branch: merchants[0].brand[0]
        } as branchinfo)
        return true
      }
      if (merchants && merchants?.length == 1 && !merchants[0]?.brand?.length) {
        cusElMessage.warning('暂无商家品牌！')
        return true
      }
      return false
    }
    function loginBtn(params?: any) {
      if (data.loginType == '3' || data.loginType == '4') {
        // 扫码登录
        store.dispatch('User/SCANLOGIN', params).then(async resopnse => {
          forget()
          let res = await getMerchant({
            b_user_id: resopnse?.data?.user_id,
            b_user_key: resopnse?.data?.key,
            b_user_val: resopnse?.data?.val,
            noloading: true
          })
          let merchants = res.data.merchant
          if (!merchants?.length) {
            cusElMessage.Toast('账号错误！')
            return
          }
          // 判断数据是否只为一条
          if (isOneData(merchants)) {
            return
          }
          state.merchants = merchants
          state.show = true
          if (params.callback) {
            params.callback()
          }
        })
        return
      }
      if (data.loginType == '2') {
        // 验证码登录
        if (!expPhone(form.phone)) {
          ElMessage.error('请输入正确的手机号码！')
          return
        }
        if (!form.code) {
          return ElMessage.error('请填写验证码！')
        }
        store
          .dispatch('User/SMSLOGIN', {
            phone: form.phone,
            code: form.code
          })
          .then(async resopnse => {
            forget()
            let res = await getMerchant({
              b_user_id: resopnse?.data?.user_id,
              b_user_key: resopnse?.data?.key,
              b_user_val: resopnse?.data?.val,
              noloading: true
            })
            let merchants = res.data.merchant
            if (!merchants?.length) {
              cusElMessage.Toast('账号错误！')
              return
            }
            // 判断数据是否只为一条
            if (isOneData(merchants)) {
              return
            }
            state.merchants = merchants
            state.show = true
          })
        return
      }

      // 账号密码登录
      if (!form.account || !form.password) {
        return ElMessage.error('请填写完整信息登录！')
      }
      store
        .dispatch('User/LOGIN', {
          account: form.account,
          passwd: form.password
          // platform_id: import.meta.env.VITE_APP_platform_id
        })
        .then(async data => {
          forget()
          let res = await getMerchant({
            b_user_id: data?.data?.user_id,
            b_user_key: data?.data?.key,
            b_user_val: data?.data?.val,
            noloading: true
          })
          let merchants = res.data.merchant
          if (!merchants?.length) {
            cusElMessage.Toast('账号错误！')
            return
          }
          // 判断数据是否只为一条
          if (isOneData(merchants)) {
            return
          }
          state.merchants = merchants
          state.show = true
        })
    }
    let password = ref<any>(null)
    function next() {
      password?.value?.focus && password?.value?.focus()
    }
    function success(data: branchinfo) {
      store.dispatch('User/SELECT_MERCH_BRANCH', data).then(() => {
        // 登录成功
        // ElMessage.success('登录成功！')
        setStore('checkPassword', 1)
        router.replace('/InitializeSystem')
        // 判断医学云根页面提示
        setStore('isRootShowTip', true)
      })
    }
    function sendCode() {
      // 发送验证码
      if (data.sending) {
        ElMessage.warning('请稍等，验证码正在路上！')
        return
      }
      if (!expPhone(form.phone)) {
        ElMessage.error('请输入正确的手机号码呦！')
        return
      }
      data.sending = true // 验证码发送中
      sendcode({ phone: form.phone, scene: 2 }).then((data: any) => {
        ElMessage.success('验证码已发送!')
      })
      sendTime()
    }
    function sendTime() {
      if (data.time <= 0) {
        data.sending = false
        data.time = 60
        return
      }
      setTimeout(() => {
        data.time--
        sendTime()
      }, 1000)
    }
    const operation = {
      down() {
        ElMessage.warning(uncultivated)
      },
      instructions() {
        ElMessage.warning(uncultivated)
      },
      contactManagement() {
        ElMessage.warning(uncultivated)
      }
    }
    const goPrivacy = () => {
      // 隐藏：医学云隐私政策跳转
      return true;
      window.open(
        'http://www.yunsop.com/static/privacy/privacy.html',
        '_blank'
      )
    }

    onMounted(() => {
      document.title = '医学云，简单可依赖！'

      useAutoLogin()
    })
    return {
      operation,
      sendCode,
      loginBtn,
      form,
      next,
      success,
      password,
      state,
      forget() {
        ElMessage.warning(uncultivated)
      },
      goPrivacy,
      changeLoginType() {
        if (data.loginType == '1' || data.loginType == '2') {
          data.loginType = '4'
        } else {
          data.loginType = '1'
          // getEqCode()
        }
      },
      ...toRefs(data)
    }
  }
})
</script>

<style lang="scss" scoped>
.login {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  overflow: hidden;
  background-color: #F5F7F9;
  // background: url('https://yakaixin.oss-cn-beijing.aliyuncs.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png')
  //   no-repeat bottom left;
  // background-size: cover;
  // background-color: var(--main-bg-color);
  user-select: none;

  .logo {
    position: absolute;
    left: 5%;
    top: 32px;
    display: flex;
    align-items: center;
  }

  .bg-animate-box {
    width: 33%;
    height: 100vh;

    /* 修改当前活动页（指示器）的样式 */
    :deep(.el-carousel__indicator.is-active button) {
      background-color: rgba(0, 0, 0, 0.2) !important;
      border: 0;
    }

    /* 设置按钮悬停时的背景色 */
    :deep(.el-carousel__button:hover) {
      background-color: rgba(0, 0, 0, 0.2);
      border: 0;
    }

    /* 设置按钮背景色 */
    :deep(.el-carousel__indicators button) {
      width: 8px;
      height: 8px;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 50%;
    }
  }

  .bg-animate {
    width: 100%;
    height: 100vh;
    background: linear-gradient(180deg, #001D97 0%, #3C64FF 100%);
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      min-width: 630px;
      // width: 67.639%;
      width: 100%;
      // height: auto;
      height: 100%;
      // margin-bottom: calc(100vh * 0.018);
    }
  }

  .logo-left-box {
    width: 67%;
  }

  .left-box {
    margin-top: 16%;
    flex: 1;
    display: flex;
    justify-content: center;

    .login-box {
      width: 340px;
      // min-height: 512px;
      // height: 512px;
      padding: 32px 32px;
      background: var(--main-bg-color);
      box-shadow: 0px 6px 30px 0px rgba(33, 36, 40, 0.1);
      background-size: cover;
      position: relative;
      border-radius: 8px;
      animation-duration: 0.8s;
      //border: 1px solid #d8dde1;
      flex-shrink: 0;
      transition: all 0.25s;

      .welcome-text {
        font-weight: 600;
        font-size: 20px;
        color: rgba(3, 32, 61, 0.85);

        .line {
          width: 63px;
          height: 4px;
          background: #d8dde1;
          margin-top: 24px;
          margin-bottom: 24px;
        }
      }

      .tabs {
        position: absolute;
        right: -1px;
        top: -1px;
        width: 67px;
        height: 67px;
        padding: 0px;
        background-color: #257cfa;
        border-top-right-radius: 8px;

        cursor: pointer;

        .bg {
          width: 55px;
          height: 55px;
          background: url('@/assets/imgs/Login/admin-login.png') no-repeat center center;
          background-size: contain;
          position: absolute;
          right: 0;
          top: 0;
        }

        .sao {
          width: 55px;
          height: 55px;
          background: url('@/assets/imgs/Login/sao-sao.png') no-repeat center center;
          background-size: contain;
          position: absolute;
          right: 0;
          top: 0;
        }

        .tabs-hover {
          opacity: 1;
          position: absolute;
          transition: all 0.1s;
          z-index: 3;
          // animation: loagin-text 8s infinite ease alternate;
          left: -120%;
          top: 0;
          bottom: 0;
          margin: auto 0;
          width: 76px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid var(--element-border-color);
          background: #257cfa;
          border-radius: 4px;
          font-size: 14px;
          font-weight: 400;
          color: #257cfa;
          line-height: 22px;
        }

        .tabs-hover::before {
          content: '';
          position: absolute;
          width: 8px;
          height: 8px;
          border-radius: 2px;
          right: -5px;
          top: 0;
          bottom: 0;
          margin: auto 0;
          border: 1px solid var(--element-border-color);
          border-left: 0 none;
          border-top: 0 none;
          background: var(--element-commen-bg-color);
          transform: rotateZ(-45deg);
        }
      }

      .tabs:hover {
        .tabs-hover {
          opacity: 1;
        }
      }

      .tabs::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        border: 34px solid #fff;
        border-top: 34px solid transparent;
        border-left: 34px solid transparent;
        transform: rotate(90deg);
      }

      .admin-password {
        .tabs-admin {
          .more {
            position: relative;
            font-weight: 400;
            font-size: 14px;
            color: rgba(3, 32, 61, 0.45);
            margin-top: 64px;
            margin-bottom: 24px;
            text-align: center;
          }

          .more::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            height: 1px;
            width: calc((100% - 72px) / 2);
            background: #d8dde1;
          }

          .more::before {
            content: '';
            position: absolute;

            right: 0;
            top: 50%;
            height: 1px;
            width: calc((100% - 72px) / 2);
            background: #d8dde1;
          }

          .el-button {
            width: 100%;
            border-radius: 8px;
            padding: 10px 16px;
            border: 1px solid #d8dde1;

            .tabs-admin-btn {
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: 500;
              font-size: 14px;
              color: #03203d;

              img {
                margin-right: 4px;
                width: 20px;
                height: 20px;
              }
            }
          }
        }

        // 账号密码登录
        .form {
          .form-item {
            font-size: 20px;
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            margin: 0 auto;
            margin-bottom: 24px;
            animation-duration: 0.8s;
            /* don't forget to set a duration! */
            animation-delay: 0.8s;

            /* don't forget to set a duration! */
            input {
              border-radius: 8px;
              border: 0 none;
              height: 40px;
              width: 100%;
              line-height: 48px;
              padding: 5px 10px;
              padding-left: 16px;
              outline: none;
              font-size: 14px;
              color: #6e7489;
              line-height: 22px;

              border: 1px solid #d8dde1;
            }

            input::-webkit-input-placeholder {
              font-size: 14px;

              font-weight: 400;
              color: rgba(3, 32, 61, 0.35);
            }

            .select {
              width: 14px;
              height: 14px;
              background: url('@/assets/imgs/input.png') no-repeat;
              background-size: cover;
              margin-right: 6px;
            }

            .selected {
              background: url('@/assets/imgs/Login/selected.png') no-repeat;
              background-size: cover;
            }

            .text {
              color: rgba(3, 32, 61, 0.85);
              font-size: 14px;
              cursor: pointer;
              user-select: none;
            }

            .forget-password {
              display: flex;
              align-items: center;

              .text {
                .btn {
                  font-weight: 400;
                  font-size: 14px;
                  color: #257cfa;
                }

                .btn:hover {
                  color: #257cfa;
                }
              }
            }

            .flex {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .forget {
                display: flex;
                align-items: center;
                justify-content: center;

                .text {
                  color: #257cfa;
                }

                .text:hover {
                  color: #257cfa;
                }
              }
            }
          }

          .form-item:nth-child(2) {
            animation-delay: 1s;
            /* don't forget to set a duration! */
            // padding-right: 24px;
            margin-bottom: 24px;
          }

          .form-item:nth-child(3) {
            animation-delay: 1.2s;
            /* don't forget to set a duration! */
          }

          .eya {
            position: absolute;
            // left: 24px;
            right: 24px;
            top: 0;
            bottom: 0;
            width: 20px;
            height: 20px;
            margin: auto 0;
            background: url('@/assets/new-icon/eyes-close.png') no-repeat;
            background-size: cover;
            cursor: pointer;
          }

          .eya-active {
            background: url('@/assets/imgs/Login/kai.png') no-repeat;
            background-size: cover;
          }

          .login-btn {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #257cfa;
            border-radius: 8px;
            animation-delay: 1.4s;

            /* don't forget to set a duration! */
            .button {
              width: 100%;
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: url('@/assets/new-icon/button-bg.png') no-repeat;
              cursor: pointer;
              color: #fff;
              user-select: none;
              font-size: 16px;
              // box-shadow: 2px 14px 16px -4px rgba(61, 204, 164, 0.24);
            }

            .button:active {
              transform: scale(0.98);
            }
          }
        }

        // 短信登录
        .short-message {
          .login-btn {
            margin-bottom: 24px;
          }

          .getCode {
            position: absolute;
            right: 16px;
            top: 0;
            bottom: 0;
            margin: auto 0;
            font-size: 14px;

            font-weight: 400;
            color: #257cfa;
            height: 21px;
            line-height: 21px;
            cursor: pointer;
          }
        }
      }

      .sao-sao {
        .question {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 15px;

          .text {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
              height: 13px;
              width: auto;
              cursor: pointer;
            }

            .zhidong {
              opacity: 0;
              transition: all 0.1s;
              position: absolute;
              left: -70%;
              right: 0;
              top: calc(100% + 10px);
              margin: 0 auto;
              width: 124px;
              height: 171px;
              background: #03203d;
              box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05),
                0px 8px 10px 1px rgba(0, 0, 0, 0.06),
                0px 5px 5px -3px rgba(0, 0, 0, 0.1);
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              display: none;

              .img {
                width: 100px;
                height: 100px;
                background-color: var(--main-bg-color);
                padding: 1px;
                margin-top: 4px;
                margin-bottom: 4px;

                img {
                  width: 100%;
                  height: auto;
                }
              }

              .zhidong-text {
                font-size: 12px;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.9);
                line-height: 22px;
                text-align: center;
                margin-right: 0;
              }
            }

            .zhidong::after {
              content: '';
              width: 10px;
              height: 10px;
              background: #03203d;
              position: absolute;
              top: -5px;
              left: 0;
              right: 0;
              margin: 0 auto;
              transform: rotateZ(45deg);
            }
          }

          .text:first-child {
            margin-right: 18px;
          }

          .active {
            display: none;
          }

          .current {
            display: block;
          }
        }

        .question .text:last-child:hover {
          .zhidong {
            opacity: 1;
            display: flex;
          }

          .active {
            display: block;
          }

          .current {
            display: none;
          }
        }
      }
    }
  }

  .checkoutLoginType {
    width: 36px;
    height: 36px;
    // background: #f2f4f5;
    background: var(--main-bg-color);
    border-radius: 6px;
    border: 1px solid #d8dfe6;
    padding: 4px;
    margin: 0 10px;
    // margin-top: 32px;
    margin-bottom: 36px;

    img {
      width: 100%;
      cursor: pointer;
    }

    position: relative;

    .imgTwo {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .xy {
      position: relative;
      top: -5px;
    }
  }

  .checkoutLoginType:hover {
    background: #f2f4f5;
  }

  .line-checkout-bottom {
    width: 340px;
    height: 1px;
    background-color: #f2f4f5;
    margin: 0 auto;
    margin-bottom: 15px;
  }
}

.footer {
  position: fixed;
  bottom: 24px;
  width: 100%;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
flex-direction: column;
  .options {
    user-select: none;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    a {
      margin-right: 32px;
      font-size: 12px;

      font-weight: 400;
      color: rgba(3, 32, 61, 0.45);
      line-height: 17px;
      cursor: pointer;
    }

    a:last-child {
      margin-right: 0;
    }
  }

  .company {
    margin-top: 20px;
    display: flex;
    align-items: center;
    font-family: HelveticaNeue;
    font-size: 12px;
    color: rgba(3, 32, 61, 0.45);
    justify-content: center;
  }
}

@keyframes loagin-text {
  0% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(10px);
  }

  50% {
    transform: translateX(0px);
  }

  75% {
    transform: translateX(10px);
  }

  100% {
    transform: translateX(0px);
  }
}

@keyframes loagin-top {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(10px);
  }

  50% {
    transform: translateY(0px);
  }

  75% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes loagin-top-fill {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(0.6);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(0.6);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes loagin-center {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(-20px);
  }

  50% {
    transform: translateY(0px);
  }

  75% {
    transform: translateY(-20px);
  }

  100% {
    transform: translateY(0px);
  }
}

@keyframes loagin-center-fill {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(0.6);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(0.6);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes loagin-bottom {
  0% {
    transform: translateY(-25px);
  }

  25% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-25px);
  }

  75% {
    transform: translateY(0px);
  }

  100% {
    // transform: translateY(0px);
    transform: translateY(-25px);
  }
}

@keyframes loagin-bottom-fill {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(0.6);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(0.6);
  }

  100% {
    transform: scale(1);
  }
}

.sao-enter-active,
.sao-leave-active {
  transition: 0.2s;
}

.sao-enter-from,
.sao-leave-from {
  opacity: 0;
}

.sao-leave-from {
  opacity: 1;
}

.sao-enter-form,
.sao-leave-to {
  opacity: 1;
}

.sao-leave-to {
  opacity: 0;
}

img {
  -webkit-user-drag: none;
}

input[type='password']::-ms-reveal {
  display: none;
}

.chartBox {
  display: flex;
  justify-content: center;
}
</style>
