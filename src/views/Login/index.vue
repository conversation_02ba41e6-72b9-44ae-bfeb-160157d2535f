<template>
  <div v-show="!showLoading">
    <!-- <login-index
      :isBgAnimate="isBgAnimate"
      :background="background"
      v-if="loginUiType == 1"
    /> -->
    <login-type1
      :isBgAnimate="isBgAnimate"
      :background="background"
    />
  </div>
  <div v-show="showLoading" class="loading">
    <img
      src="https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/public/17159114001032cb7171591140010327680_l.gif"
      alt=""
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, computed, onMounted,reactive, ref, toRefs } from 'vue'
import loginIndex from '@/components/AppSystem/login-pages/index.vue'
import loginType1 from '@/components/AppSystem/login-pages/login-type1.vue'
import { getSystem } from '@/utils'
import { authLogOut } from '@/utils/thirdParty'
import { useRoute, useRouter } from 'vue-router'
import { cusElMessage, ElMessage } from '@/utils/element'
import { useStore } from 'vuex'
import { setStore, getStore, clearStore } from '@/utils/localStore'
import { expPhone } from '@/utils/Reg'
import selectBranch from '@/components/BusinessCenter/public/select-branch.vue'
import type { branchinfo } from '@/store/modules/User/index.type'
import { getMerchant, sendcode, sendcodePublic } from '@/api/index'
import { uncultivated } from '@/config/index'
import dingtalk from '@/components/AppSystem/login/dingtalk.vue'
import wchat from '@/components/AppSystem/login/wchat.vue'
import { useAutoLogin } from '@/CompositionHooksApi/useAutoLogin'
import { isScss } from '@/utils'
export default defineComponent({
  name: 'Login',
  props: {
    isBgAnimate: {
      // 是否显示左侧动态效果图
      type: Boolean,
      default: true
    },
    background: {
      // 登录页背景图
      type: String,
      default: `url('https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png') no-repeat bottom left`
      // default:
      //   'https://xycassets.yunsop.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png'
    }
  },
  components: { loginIndex, loginType1 },
  setup() {
    const router = useRouter()
    const form = reactive({
      // 账号密码
      account: '',
      password: '',
      // 手机密码
      phone: '',
      code: ''
    })
    const state = reactive({
      forget: false,
      merchants: [],
      show: false,
      eya: true
    })
    const data = reactive({
      loginType: isScss() ? '4' : '1', // 1 账号密码 2 短信登录 3 扫码登录(钉钉) 4. 扫码登陆（企业微信）
      // 验证码是否已经送
      sending: false,
      time: 60 // 60s倒计时
    })
    const store = useStore()
    function passwordInit() {
      try {
        let account = getStore('__account__')
        console.log(account)
        let password = getStore('__password__')
        if (account && password) {
          form.account = account
          form.password = password
          state.forget = true
        } else {
          state.forget = false
        }
      } catch (e) {
        console.log(e)
      }
    }
    passwordInit()
    function forget() {
      if (state.forget) {
        // 记住密码
        setStore('__account__', form.account)
        setStore('__password__', form.password)
      } else {
        // 清除登录
        clearStore('__account__')
        clearStore('__password__')
      }
    }
    function isOneData(merchants: any[]): boolean {
      if (
        merchants &&
        merchants?.length == 1 &&
        merchants[0]?.brand?.length &&
        merchants[0].brand.length == 1
      ) {
        success({
          merchant: merchants[0],
          branch: merchants[0].brand[0]
        } as branchinfo)
        return true
      }
      if (merchants && merchants?.length == 1 && !merchants[0]?.brand?.length) {
        cusElMessage.warning('暂无商家品牌！')
        return true
      }
      return false
    }
    function loginBtn(params?: any) {
      if (data.loginType == '3' || data.loginType == '4') {
        // 扫码登录
        store.dispatch('User/SCANLOGIN', params).then(async resopnse => {
          forget()
          let res = await getMerchant({
            b_user_id: resopnse?.data?.user_id,
            b_user_key: resopnse?.data?.key,
            b_user_val: resopnse?.data?.val,
            noloading: true
          })
          let merchants = res.data.merchant
          if (!merchants?.length) {
            cusElMessage.Toast('账号错误！')
            return
          }
          // 判断数据是否只为一条
          if (isOneData(merchants)) {
            return
          }
          state.merchants = merchants
          state.show = true
          if (params.callback) {
            params.callback()
          }
        })
        return
      }
      if (data.loginType == '2') {
        // 验证码登录
        if (!expPhone(form.phone)) {
          ElMessage.error('请输入正确的手机号码！')
          return
        }
        if (!form.code) {
          return ElMessage.error('请填写验证码！')
        }
        store
          .dispatch('User/SMSLOGIN', {
            phone: form.phone,
            code: form.code
          })
          .then(async resopnse => {
            forget()
            let res = await getMerchant({
              b_user_id: resopnse?.data?.user_id,
              b_user_key: resopnse?.data?.key,
              b_user_val: resopnse?.data?.val,
              noloading: true
            })
            let merchants = res.data.merchant
            if (!merchants?.length) {
              cusElMessage.Toast('账号错误！')
              return
            }
            // 判断数据是否只为一条
            if (isOneData(merchants)) {
              return
            }
            state.merchants = merchants
            state.show = true
          })
        return
      }

      // 账号密码登录
      if (!form.account || !form.password) {
        return ElMessage.error('请填写完整信息登录！')
      }
      store
        .dispatch('User/LOGIN', {
          account: form.account,
          passwd: form.password
          // platform_id: import.meta.env.VITE_APP_platform_id
        })
        .then(async data => {
          forget()
          let res = await getMerchant({
            b_user_id: data?.data?.user_id,
            b_user_key: data?.data?.key,
            b_user_val: data?.data?.val,
            noloading: true
          })
          let merchants = res.data.merchant
          if (!merchants?.length) {
            cusElMessage.Toast('账号错误！')
            return
          }
          // 判断数据是否只为一条
          if (isOneData(merchants)) {
            return
          }
          state.merchants = merchants
          state.show = true
        })
    }
    let password = ref<any>(null)
    function next() {
      password?.value?.focus && password?.value?.focus()
    }
    function success(data: branchinfo) {
      store.dispatch('User/SELECT_MERCH_BRANCH', data).then(() => {
        // 登录成功
        // ElMessage.success('登录成功！')
        setStore('checkPassword', 1)
        router.replace('/InitializeSystem')
        // 判断医学云根页面提示
        setStore('isRootShowTip', true)
      })
    }
    function sendCode() {
      // 发送验证码
      if (data.sending) {
        ElMessage.warning('请稍等，验证码正在路上！')
        return
      }
      if (!expPhone(form.phone)) {
        ElMessage.error('请输入正确的手机号码呦！')
        return
      }
      data.sending = true // 验证码发送中
      sendMessage(
        {
          mobile: form.phone,
          scene: 2
        },
        'jyj_tencentclound_captcha'
      ).then((data: any) => {
        ElMessage.success('验证码已发送!')
      })
      // sendcode({ phone: form.phone, scene: 2 }).then((data: any) => {
      //   ElMessage.success('验证码已发送!')
      // })
      sendTime()
    }
    function sendTime() {
      if (data.time <= 0) {
        data.sending = false
        data.time = 60
        return
      }
      setTimeout(() => {
        data.time--
        sendTime()
      }, 1000)
    }
    const operation = {
      down() {
        ElMessage.warning(uncultivated)
      },
      instructions() {
        ElMessage.warning(uncultivated)
      },
      contactManagement() {
        ElMessage.warning(uncultivated)
      }
    }
    onMounted(() => {
      setTimeout(() => {
        // initRequest(sendcodePublic, sendcode)
      }, 300)
      document.title = '医学云，简单可依赖！'

      useAutoLogin()
    })
    return {
      loginUiType: computed(() => {
        return window.system.loginUiType
      }),
      showLoading: getSystem() == 'spc'
    }
  }
})
</script>

<style lang="scss" scoped>
.loading {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 110px;
  padding-right: 180px;
  overflow: hidden;
  // background: url('https://xycassets.yunsop.com/public/165106021376751a8165106021376793544_new-login-bg.ed21fbae.png')
  //   no-repeat bottom left;
  // background-size: cover;
  // background-color: var(--main-bg-color);
  position: relative;
  user-select: none;
  .logo {
    position: absolute;
    left: 32px;
    top: 24px;
    display: flex;
    align-items: center;
    img {
      width: 32px;
      height: 32px;
      margin-right: 6px;
    }
    span {
      font-size: 18px;

      font-weight: 800;
      color: #03203d;
      line-height: 25px;
      letter-spacing: 1px;
    }
  }
  .bg-animate {
    width: 584px;
    height: 269px;
    flex-shrink: 0;
    // background: url('https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/public/16510603207068aaf165106032070692233_new-animte.a4543383.png');
    // background: url('https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/public/16528626498991239165286264989922192_bg-ani.png');
    background: url('https://xy-shunshun-pro.oss-cn-hangzhou.aliyuncs.com/public/16528626498991239165286264989922192_bg-ani.png');

    background-size: cover;
    position: relative;
    margin-right: 117px;
    .top {
      position: absolute;
      left: 106px;
      top: -34px;
      width: 134px;
      height: 69px;
      img:nth-child(1) {
        width: 134px;
        height: 69px;
        position: relative;
        z-index: 2;
        animation: loagin-top 8s infinite ease alternate;
      }
      img:nth-child(2) {
        width: 149px;
        height: 70px;
        position: absolute;
        left: -3px;
        top: 35px;
        z-index: 1;
        animation: loagin-top-fill 8s infinite ease alternate;
      }
    }
    .center {
      position: absolute;
      left: 227px;
      top: 5px;
      width: 184px;
      height: 91px;
      img:nth-child(1) {
        width: 184px;
        height: 91px;
        position: relative;
        z-index: 2;
        animation: loagin-center 8s infinite ease alternate;
      }
      img:nth-child(2) {
        width: 189px;
        height: 99px;
        position: absolute;
        left: -3px;
        top: 34px;
        z-index: 1;
        animation: loagin-center-fill 8s infinite ease alternate;
      }
    }
    .bottom {
      position: absolute;
      left: 136px;
      top: 82px;
      width: 120px;
      height: 63px;
      img:nth-child(1) {
        width: 120px;
        height: 63px;
        position: relative;
        z-index: 2;
        animation: loagin-bottom 8s infinite ease alternate;
      }
      img:nth-child(2) {
        width: 152px;
        height: 94px;
        position: absolute;
        left: -9px;
        top: 13px;
        z-index: 1;
        animation: loagin-bottom-fill 8s infinite ease alternate;
      }
    }
  }
  .login-box {
    width: 448px;
    // min-height: 512px;
    height: 512px;
    padding: 40px 32px;
    background: var(--main-bg-color);
    box-shadow: 0px 6px 30px 0px rgba(33, 36, 40, 0.1);
    background-size: cover;
    position: relative;
    border-radius: 8px;
    animation-duration: 0.8s;
    border: 1px solid #d8dde1;
    flex-shrink: 0;
    transition: all 0.25s;
    .tabs {
      position: absolute;
      right: -1px;
      top: -1px;
      width: 67px;
      height: 67px;
      padding: 0px;
      background-color: var(--main-color);
      border-top-right-radius: 8px;

      cursor: pointer;
      .bg {
        width: 55px;
        height: 55px;
        background: url('@/assets/imgs/Login/admin-login.png') no-repeat center
          center;
        background-size: contain;
        position: absolute;
        right: 0;
        top: 0;
      }
      .sao {
        width: 55px;
        height: 55px;
        background: url('@/assets/imgs/Login/sao-sao.png') no-repeat center
          center;
        background-size: contain;
        position: absolute;
        right: 0;
        top: 0;
      }
      .tabs-hover {
        opacity: 1;
        position: absolute;
        transition: all 0.1s;
        z-index: 3;
        // animation: loagin-text 8s infinite ease alternate;
        left: -120%;
        top: 0;
        bottom: 0;
        margin: auto 0;
        width: 76px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--element-border-color);
        background: var(--element-commen-bg-color);
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: var(--main-color);
        line-height: 22px;
      }
      .tabs-hover::before {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 2px;
        right: -5px;
        top: 0;
        bottom: 0;
        margin: auto 0;
        border: 1px solid var(--element-border-color);
        border-left: 0 none;
        border-top: 0 none;
        background: var(--element-commen-bg-color);
        transform: rotateZ(-45deg);
      }
    }
    .tabs:hover {
      .tabs-hover {
        opacity: 1;
      }
    }

    .tabs::after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      border: 34px solid #fff;
      border-top: 34px solid transparent;
      border-left: 34px solid transparent;
      transform: rotate(90deg);
    }
    .admin-password {
      .tabs-admin {
        display: flex;
        align-items: center;
        margin-bottom: 40px;
        position: relative;
        // justify-content: center;
        .tab-admin {
          height: 28px;
          line-height: 28px;
          font-size: 20px;

          font-weight: 600;
          color: rgba(3, 32, 61, 0.65);
          line-height: 28px;
          margin-right: 32px;
          cursor: pointer;
        }
        .active {
          font-size: 20px;

          font-weight: 600;
          color: #03203d;
        }
        .line {
          position: absolute;
          bottom: -10px;
          width: 80px;
          height: 4px;
          background: var(--main-color);
          border-radius: 2px;
          transition: all 0.25s;
        }
        .line {
          left: 0;
        }
        .line2 {
          left: 112px;
        }
      }
      // 账号密码登录
      .form {
        .form-item {
          font-size: 20px;
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          margin: 0 auto;
          margin-bottom: 28px;
          animation-duration: 0.8s; /* don't forget to set a duration! */
          animation-delay: 0.8s; /* don't forget to set a duration! */
          input {
            border-radius: 4px;
            border: 0 none;
            height: 48px;
            width: 100%;
            line-height: 48px;
            padding: 5px 10px;
            padding-left: 16px;
            outline: none;
            font-size: 16px;
            color: #6e7489;
            line-height: 22px;

            border: 1px solid #d8dde1;
          }
          input::-webkit-input-placeholder {
            font-size: 14px;

            font-weight: 400;
            color: rgba(3, 32, 61, 0.35);
          }
          .select {
            width: 14px;
            height: 14px;
            background: url('../../assets/imgs/input.png') no-repeat;
            background-size: cover;
            margin-right: 6px;
          }
          .selected {
            background: url('../../assets/imgs/Login/selected.png') no-repeat;
            background-size: cover;
          }
          .text {
            color: rgba(3, 32, 61, 0.85);
            font-size: 14px;
            cursor: pointer;
            user-select: none;
          }
          .forget-password {
            display: flex;
            align-items: center;
          }
          .flex {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            .forget {
              display: flex;
              align-items: center;
              justify-content: center;
              .text:hover {
                color: var(--main-color);
              }
            }
          }
        }
        .form-item:nth-child(2) {
          animation-delay: 1s; /* don't forget to set a duration! */
          // padding-right: 24px;
          margin-bottom: 32px;
        }
        .form-item:nth-child(3) {
          animation-delay: 1.2s; /* don't forget to set a duration! */
        }
        .eya {
          position: absolute;
          // left: 24px;
          right: 24px;
          top: 0;
          bottom: 0;
          width: 20px;
          height: 20px;
          margin: auto 0;
          background: url('../../assets/imgs/Login/close.png') no-repeat;
          background-size: cover;
          cursor: pointer;
        }
        .eya-active {
          background: url('../../assets/imgs/Login/kai.png') no-repeat;
          background-size: cover;
        }
        .login-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          animation-delay: 1.4s; /* don't forget to set a duration! */
          margin-top: 88px;
          .button {
            width: 100%;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--main-color);
            border-radius: 4px;
            cursor: pointer;
            color: #fff;
            user-select: none;
            font-size: 16px;
            // box-shadow: 2px 14px 16px -4px rgba(61, 204, 164, 0.24);
          }
          .button:active {
            transform: scale(0.98);
          }
        }
      }
      // 短信登录
      .short-message {
        .login-btn {
          margin-top: 136px;
        }
        .getCode {
          position: absolute;
          right: 16px;
          top: 0;
          bottom: 0;
          margin: auto 0;
          font-size: 15px;

          font-weight: 400;
          color: var(--main-color);
          height: 21px;
          line-height: 21px;
          cursor: pointer;
        }
      }
    }
    .sao-sao {
      .question {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 15px;
        .text {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            height: 13px;
            width: auto;
            cursor: pointer;
          }
          .zhidong {
            opacity: 0;
            transition: all 0.1s;
            position: absolute;
            left: -70%;
            right: 0;
            top: calc(100% + 10px);
            margin: 0 auto;
            width: 124px;
            height: 171px;
            background: #03203d;
            box-shadow: 0px 3px 14px 2px rgba(0, 0, 0, 0.05),
              0px 8px 10px 1px rgba(0, 0, 0, 0.06),
              0px 5px 5px -3px rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            display: none;
            .img {
              width: 100px;
              height: 100px;
              background-color: var(--main-bg-color);
              padding: 1px;
              margin-top: 4px;
              margin-bottom: 4px;
              img {
                width: 100%;
                height: auto;
              }
            }
            .zhidong-text {
              font-size: 12px;

              font-weight: 400;
              color: rgba(255, 255, 255, 0.9);
              line-height: 22px;
              text-align: center;
              margin-right: 0;
            }
          }
          .zhidong::after {
            content: '';
            width: 10px;
            height: 10px;
            background: #03203d;
            position: absolute;
            top: -5px;
            left: 0;
            right: 0;
            margin: 0 auto;
            transform: rotateZ(45deg);
          }
        }
        .text:first-child {
          margin-right: 18px;
        }
        .active {
          display: none;
        }
        .current {
          display: block;
        }
      }
      .question .text:last-child:hover {
        .zhidong {
          opacity: 1;
          display: flex;
        }
        .active {
          display: block;
        }
        .current {
          display: none;
        }
      }
    }
  }

  .login-center {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .checkoutLoginType {
    width: 36px;
    height: 36px;
    // background: #f2f4f5;
    background: var(--main-bg-color);
    border-radius: 6px;
    border: 1px solid #d8dfe6;
    padding: 4px;
    margin: 0 10px;
    // margin-top: 32px;
    margin-bottom: 36px;
    position: relative;
    img {
      width: 100%;
      cursor: pointer;
    }
    .imgTwo {
      position: absolute;
      bottom: 0;
      left: 0;
    }
    .xy {
      position: relative;
      top: -5px;
    }
  }
  .checkoutLoginType:hover {
    background: #f2f4f5;
  }
  .line-checkout-bottom {
    width: 340px;
    height: 1px;
    background-color: #f2f4f5;
    margin: 0 auto;
    margin-bottom: 15px;
  }
}
.footer {
  position: fixed;
  bottom: 16px;
  width: 100%;
  left: 0;
  .options {
    display: flex;
    align-items: center;
    justify-content: center;
    a {
      margin-right: 32px;
      font-size: 12px;

      font-weight: 400;
      color: rgba(3, 32, 61, 0.35);
      line-height: 17px;
      cursor: pointer;
    }
    a:last-child {
      margin-right: 0;
    }
  }
  .tip {
    height: 48px;
    line-height: 48px;
    font-size: 12px;
    color: rgba(3, 32, 61, 0.35);
    line-height: 48px;
    text-align: center;
    margin-top: 8px;
  }
}
@keyframes loagin-text {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(10px);
  }
  50% {
    transform: translateX(0px);
  }
  75% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0px);
  }
}
@keyframes loagin-top {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(10px);
  }
  50% {
    transform: translateY(0px);
  }
  75% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes loagin-top-fill {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.6);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.6);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loagin-center {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(0px);
  }
  75% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes loagin-center-fill {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.6);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.6);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes loagin-bottom {
  0% {
    transform: translateY(-25px);
  }
  25% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25px);
  }
  75% {
    transform: translateY(0px);
  }
  100% {
    // transform: translateY(0px);
    transform: translateY(-25px);
  }
}
@keyframes loagin-bottom-fill {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.6);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.6);
  }
  100% {
    transform: scale(1);
  }
}
.sao-enter-active,
.sao-leave-active {
  transition: 0.2s;
}
.sao-enter-from,
.sao-leave-from {
  opacity: 0;
}
.sao-leave-from {
  opacity: 1;
}
.sao-enter-form,
.sao-leave-to {
  opacity: 1;
}
.sao-leave-to {
  opacity: 0;
}
img {
  -webkit-user-drag: none;
}

input[type='password']::-ms-reveal {
  display: none;
}

.chartBox {
  display: flex;
  justify-content: center;
}
</style>
