{"name": "tms-vite-new", "version": "0.0.1", "scripts": {"dev": "cross-env VITE_APP_TYPE=scss cross-env vite --force", "dev-test": "cross-env VITE_APP_TYPE=scss cross-env vite --force --mode testdevelopment", "dev-pre": "cross-env VITE_APP_TYPE=scss cross-env vite --force --mode predevelopment", "dev-uat": "cross-env VITE_APP_TYPE=scss cross-env vite --force --mode uatdevelopment", "qb-dev": "cross-env VITE_APP_TYPE=questionBlank cross-env vite --force", "qb-dev-test": "cross-env VITE_APP_TYPE=questionBlank cross-env vite --force --mode testdevelopment", "qb-dev-pre": "cross-env VITE_APP_TYPE=questionBlank cross-env vite --force --mode predevelopment", "ys-dev": "cross-env VITE_APP_TYPE=ysysJob cross-env vite --force", "ys-dev-test": "cross-env VITE_APP_TYPE=ysysJob cross-env vite --force --mode testdevelopment", "ys-dev-pre": "cross-env VITE_APP_TYPE=ysysJob cross-env vite --force --mode predevelopment", "serve": "cross-env VITE_APP_TYPE=scss cross-env vite", "build": "cross-env VITE_APP_TYPE=scss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build && node ./version.js", "build-dev": "set vis=true && cross-env VITE_APP_TYPE=scss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode development && node ./version.js", "build-test": "set vis=true && cross-env VITE_APP_TYPE=scss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode test && node ./version.js", "build-uat": "cross-env VITE_APP_TYPE=scss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build && node ./version.js", "build-pre": "set vis=true && cross-env VITE_APP_TYPE=scss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode pre && node ./version.js", "dev-singleScss": "cross-env VITE_APP_TYPE=singleScss cross-env vite --force", "dev-singleScss-pre": "cross-env VITE_APP_TYPE=singleScss cross-env vite --force --mode predevelopment", "build-singleScss": "cross-env VITE_APP_TYPE=singleScss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build && node ./version.js", "build-singleScss-dev": "set vis=true && cross-env VITE_APP_TYPE=singleScss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode development && node ./version.js", "build-singleScss-test": "set vis=true && cross-env VITE_APP_TYPE=singleScss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode test && node ./version.js", "build-singleScss-pre": "set vis=true && cross-env VITE_APP_TYPE=singleScss cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode pre && node ./version.js", "ys-build-dev": "set vis=true && cross-env VITE_APP_TYPE=ysysJob cross-env vite build --mode development && node ./version.js", "ys-build-test": "set vis=true && cross-env VITE_APP_TYPE=ysysJob cross-env vite build --mode test && node ./version.js", "ys-build-pre": "set vis=true && cross-env VITE_APP_TYPE=ysysJob cross-env vite build --mode pre && node ./version.js", "preview": "cross-env vite preview", "lint": "cross-env eslint", "lint:all": "prettier --config prettier.config.js src/**/*.{vue,js,ts} --write", "dev-analysis": "cross-env VITE_APP_TYPE=analysis cross-env vite --force", "build-analysis-test": "cross-env VITE_APP_TYPE=analysis cross-env vite build --mode test && node ./version.js", "build-analysis-pro": "cross-env VITE_APP_TYPE=analysis cross-env vite build && node ./version.js", "qb-build-dev": "set vis=true && cross-env VITE_APP_TYPE=questionBlank cross-env vite build --mode development", "qb-build-test": "set vis=true && cross-env VITE_APP_TYPE=questionBlank cross-env vite build --mode test", "qb-build-pre": "set vis=true && cross-env VITE_APP_TYPE=questionBlank cross-env vite build --mode pre", "teaching-dev": "cross-env VITE_APP_TYPE=teaching cross-env vite --force", "teaching-dev-test": "cross-env VITE_APP_TYPE=teaching cross-env vite --force --mode testdevelopment", "teaching-dev-pre": "cross-env VITE_APP_TYPE=teaching cross-env vite --force --mode predevelopment", "build-teaching": "cross-env VITE_APP_TYPE=teaching cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build && node ./version.js", "build-teaching-dev": "set vis=true && cross-env VITE_APP_TYPE=teaching cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode development && node ./version.js", "build-teaching-test": "set vis=true && cross-env VITE_APP_TYPE=teaching cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode test && node ./version.js", "build-teaching-pre": "set vis=true && cross-env VITE_APP_TYPE=teaching cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode pre && node ./version.js", "spc-dev": "cross-env VITE_APP_TYPE=spc cross-env vite --force", "spc-dev-test": "cross-env VITE_APP_TYPE=spc cross-env vite --force --mode testdevelopment", "spc-dev-pre": "cross-env VITE_APP_TYPE=spc cross-env vite --force --mode predevelopment", "build-spc": "cross-env VITE_APP_TYPE=spc cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build && node ./version.js && node ./clear.js", "build-spc-dev": "set vis=true && cross-env VITE_APP_TYPE=spc cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode development && node ./version.js && node ./clear.js", "build-spc-test": "set vis=true && cross-env VITE_APP_TYPE=spc cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode test && node ./version.js && node ./clear.js", "build-spc-pre": "set vis=true && cross-env VITE_APP_TYPE=spc cross-env node --max_old_space_size=6144 ./node_modules/vite/bin/vite.js build --mode pre && node ./version.js && node ./clear.js", "dev-sassPro": "cross-env VITE_APP_TYPE=sassPro cross-env vite --force", "dev-sassPro-pre": "cross-env VITE_APP_TYPE=sassPro cross-env vite --force --mode predevelopment", "build-sassPro": "cross-env VITE_APP_TYPE=sassPro cross-env vite build && node ./version.js", "build-sassPro-dev": "set vis=true && cross-env VITE_APP_TYPE=sassPro cross-env vite build --mode development && node ./version.js", "build-sassPro-test": "set vis=true && cross-env VITE_APP_TYPE=sassPro cross-env vite build --mode test && node ./version.js", "build-sassPro-pre": "set vis=true && cross-env VITE_APP_TYPE=sassPro cross-env vite build --mode pre && node ./version.js", "dev-sassMax": "cross-env VITE_APP_TYPE=sassMax cross-env vite --force", "dev-sassMax-pre": "cross-env VITE_APP_TYPE=sassMax cross-env vite --force --mode predevelopment", "build-sassMax": "cross-env VITE_APP_TYPE=sassMax cross-env vite build && node ./version.js", "build-sassMax-dev": "set vis=true && cross-env VITE_APP_TYPE=sassMax cross-env vite build --mode development && node ./version.js", "build-sassMax-test": "set vis=true && cross-env VITE_APP_TYPE=sassMax cross-env vite build --mode test && node ./version.js", "build-sassMax-pre": "set vis=true && cross-env VITE_APP_TYPE=sassMax cross-env vite build --mode pre && node ./version.js", "bin": "node bin/independentDeployment"}, "license": "ISC", "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@arco-design/web-vue": "^2.34.1", "@arco-themes/vue-jyj-and": "^0.0.1", "@element-plus/icons-vue": "^0.2.6", "@tinymce/tinymce-vue": "^5.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "add": "^2.0.6", "ali-oss": "^6.17.1", "amfe-flexible": "^2.2.1", "arale-qrcode": "^3.0.5", "axios": "^0.21.4", "babel-polyfill": "^6.26.0", "consola": "^3.2.3", "core-js": "^3.6.5", "dayjs": "^1.10.3", "docx-preview": "^0.1.15", "echarts": "^5.3.1", "element-plus": "1.1.0-beta.24", "howler": "^2.2.4", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "js-base64": "^3.7.2", "js-sha1": "^0.6.0", "jsencrypt": "^3.2.1", "jspdf": "^2.5.1", "md5": "^2.3.0", "mitt": "^3.0.0", "nanoid": "^4.0.0", "nprogress": "^0.2.0", "pdfjs-dist": "3.5.141", "rollup-plugin-terser": "^7.0.2", "socket.io-client": "^2.0.3", "sortablejs": "^1.14.0", "swiper": "8.0.7", "tcplayer.js": "^4.7.0", "tinymce": "^6.5.0", "vite-plugin-compression": "^0.3.6", "vite-plugin-style-import": "^2.0.0", "vod-js-sdk-v6": "^1.7.0", "vue": "^3.2.21", "vue-clipboard3": "2.0.0", "vue-draggable-next": "^2.1.1", "vue-draggable-resizable": "^2.3.0", "vue-i18n": "^9.2.0-beta.13", "vue-material-year-calendar": "^1.2.6", "vue-router": "^4.0.0-0", "vue3-draggable-resizable": "^1.6.5", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "vxe-table": "4.5.12", "wangeditor": "^4.7.12", "wpk-reporter": "^0.9.3", "xe-utils": "^3.5.13", "yarn": "^1.22.19"}, "devDependencies": {"@types/ali-oss": "^6.16.2", "@types/howler": "^2.2.11", "@types/jest": "^24.0.19", "@types/node": "^16.11.11", "@types/nprogress": "^0.2.0", "@types/socket.io-client": "^3.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vitejs/plugin-legacy": "1.8.2", "@vitejs/plugin-vue": "^1.9.3", "@vitejs/plugin-vue-jsx": "^1.3.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-jest": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-standard": "^6.1.0", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^2.0.0-0", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^7.0.3", "element-theme-chalk": "^2.15.6", "eslint": "^6.7.2", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-vue": "^8.2.0", "file-saver": "^2.0.5", "install": "^0.13.0", "mockjs": "^1.1.0", "npm": "^8.6.0", "postcss-pxtorem": "^6.0.0", "prettier": "^2.5.1", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "typescript": "^4.4.3", "unplugin-vue-components": "^0.17.6", "vite": "^2.6.4", "vite-plugin-checker": "^0.3.4", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-eslint": "^1.3.0", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vue-jest": "^5.0.0-0", "xlsx": "^0.18.5"}, "resolutions": {"path2d-polyfill": "^2.0.0"}, "vite": {"optimizeDeps": {"include": ["@amap/amap-jsapi-loader", "@element-plus/icons-vue", "ali-oss", "axios", "echarts", "element-plus", "element-plus/es/components/button", "element-plus/es/locale/lang/zh-cn", "element-plus/lib/locale/lang/en", "element-plus/lib/locale/lang/zh-cn", "file-saver", "html2canvas", "js-base64", "js-sha1", "jsencrypt/bin/jsencrypt", "md5", "mitt", "mockjs", "nprogress", "process", "qs", "sortablejs", "vue", "vue-draggable-next", "vue-i18n", "vue-router", "vuex", "wangeditor", "xlsx"]}}}